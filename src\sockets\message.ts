import { createServer } from "http";
import { Server } from "socket.io";

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: { origin: "*" }, // allow all origins for testing
});

io.on("connection", (socket) => {
  console.log("User connected");

  // send server time immediately
  socket.emit("serverTime", { time: Date.now() });

  // optional: keep updating every second
  const interval = setInterval(() => {
    socket.emit("serverTime", { time: Date.now() });
  }, 1000);

  socket.on("disconnect", () => {
    clearInterval(interval);
    console.log("User disconnected");
  });
});

httpServer.listen(3000, () => {
  console.log("Socket server running on :3000");
});
